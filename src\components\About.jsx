import React from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import { FaCode, FaPalette, FaRocket, FaUsers } from 'react-icons/fa'

const About = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, threshold: 0.1 })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  const features = [
    {
      icon: FaCode,
      title: 'Clean Code',
      description: 'Writing maintainable, scalable, and efficient code following best practices.'
    },
    {
      icon: FaPalette,
      title: 'Creative Design',
      description: 'Crafting beautiful and intuitive user interfaces that enhance user experience.'
    },
    {
      icon: FaRocket,
      title: 'Fast Performance',
      description: 'Optimizing applications for speed and performance across all devices.'
    },
    {
      icon: FaUsers,
      title: 'Team Player',
      description: 'Collaborating effectively with cross-functional teams to deliver great products.'
    }
  ]

  return (
    <section id="about" className="py-20 bg-gray-50" ref={ref}>
      <div className="container mx-auto px-6">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-6xl mx-auto"
        >
          {/* Section Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
              About <span className="gradient-text">Me</span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto rounded-full"></div>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Image and Stats */}
            <motion.div variants={itemVariants} className="space-y-8">
              <div className="relative">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.3 }}
                  className="relative z-10"
                >
                  <img
                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face"
                    alt="Profile"
                    className="w-80 h-80 object-cover rounded-2xl shadow-2xl mx-auto"
                  />
                </motion.div>
                <div className="absolute -top-4 -right-4 w-80 h-80 bg-gradient-to-br from-blue-400 to-purple-400 rounded-2xl -z-10"></div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 text-center">
                {[
                  { number: '50+', label: 'Projects' },
                  { number: '3+', label: 'Years Exp' },
                  { number: '100%', label: 'Satisfaction' }
                ].map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    variants={itemVariants}
                    whileHover={{ scale: 1.05 }}
                    className="bg-white p-4 rounded-xl shadow-lg"
                  >
                    <div className="text-2xl font-bold gradient-text">{stat.number}</div>
                    <div className="text-gray-600 text-sm">{stat.label}</div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Right Column - Content */}
            <motion.div variants={itemVariants} className="space-y-6">
              <h3 className="text-3xl font-bold text-gray-800 mb-4">
                Passionate Developer & Designer
              </h3>
              
              <p className="text-gray-600 leading-relaxed">
                I'm a passionate full-stack developer with over 3 years of experience creating 
                digital experiences that are not only functional but also beautiful and user-friendly. 
                My journey in web development started with a curiosity about how things work, 
                and it has evolved into a career that I absolutely love.
              </p>

              <p className="text-gray-600 leading-relaxed">
                I specialize in React, Node.js, and modern web technologies. I'm always eager to 
                learn new technologies and take on challenges that push me to grow as a developer. 
                When I'm not coding, you can find me exploring new design trends, contributing to 
                open-source projects, or sharing knowledge with the developer community.
              </p>

              {/* Features Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
                {features.map((feature, index) => (
                  <motion.div
                    key={feature.title}
                    variants={itemVariants}
                    whileHover={{ scale: 1.05, y: -5 }}
                    className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <feature.icon className="text-3xl text-blue-600 mb-3" />
                    <h4 className="font-semibold text-gray-800 mb-2">{feature.title}</h4>
                    <p className="text-gray-600 text-sm">{feature.description}</p>
                  </motion.div>
                ))}
              </div>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 mt-6"
                onClick={() => document.querySelector('#contact').scrollIntoView({ behavior: 'smooth' })}
              >
                Let's Work Together
              </motion.button>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default About
